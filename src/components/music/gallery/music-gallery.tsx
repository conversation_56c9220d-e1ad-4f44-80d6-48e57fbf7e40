"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>bs, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Filter,
  Music,
  Plus,
  Trash2,
  Share2,
  Clock,
  Calendar,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import TrackCard from "@/components/music/track/track-card";
import { Track } from "@/types/music";
import Link from "next/link";
import Pagination, { PaginationInfo } from "@/components/ui/pagination";

interface UserTrack extends Track {
  generation_status?: "pending" | "processing" | "completed" | "failed";
  is_favorite?: boolean;
  folder_id?: string;
  tags?: string[];
}

export default function MusicGallery() {
  const { data: session, status } = useSession();
  const [tracks, setTracks] = useState<UserTrack[]>([]);
  const [filteredTracks, setFilteredTracks] = useState<UserTrack[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedStyle, setSelectedStyle] = useState("all");
  const [sortBy, setSortBy] = useState("recent");
  const [isLoading, setIsLoading] = useState(true);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(20);
  const [totalTracks, setTotalTracks] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Available filter options
  const [availableStyles, setAvailableStyles] = useState<string[]>([]);
  const [availableMoods, setAvailableMoods] = useState<string[]>([]);

  // Filter panel state for mobile
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  // Audio player state
  const [currentPlayingTrack, setCurrentPlayingTrack] = useState<string | null>(null);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  // Load user's tracks using the track API with pagination and filtering
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      loadUserTracks();
    }
  }, [status, session, currentPage, perPage, searchQuery, selectedStatus, selectedStyle, sortBy]);

  const loadUserTracks = async () => {
    try {
      setIsLoading(true);

      // Build search parameters
      const searchParams: any = {
        user_uuid: session?.user?.uuid,
        page: currentPage,
        per_page: perPage,
        sort_by: sortBy === "recent" ? "created_at" : sortBy === "oldest" ? "created_at" : sortBy,
        sort_order: sortBy === "recent" ? "desc" : sortBy === "oldest" ? "asc" : "desc",
      };

      // Add search query if provided
      if (searchQuery.trim()) {
        searchParams.search = searchQuery.trim();
      }

      // Add style filter if selected
      if (selectedStyle !== "all") {
        searchParams.style = selectedStyle;
      }

      // Add status-based filters
      if (selectedStatus === "public") {
        searchParams.is_public = true;
      } else if (selectedStatus === "private") {
        searchParams.is_public = false;
      }
      // Note: "verified" filter will be handled client-side for now

      // Use the track API with pagination and filtering
      const response = await fetch("/api/music/tracks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(searchParams),
      });

      const data = await response.json();

      if (response.ok && data.code === 0) {
        const { tracks, pagination, filters } = data.data;

        setTracks(tracks || []);
        setTotalTracks(pagination.total);
        setTotalPages(pagination.total_pages);
        setHasMore(pagination.has_more);

        // Update available filter options
        if (filters) {
          setAvailableStyles(filters.available_styles || []);
          setAvailableMoods(filters.available_moods || []);
        }
      } else {
        console.error("Failed to load tracks:", data.message);
        toast.error("Failed to load your music library");
      }
    } catch (error) {
      console.error("Failed to load tracks:", error);
      toast.error("Failed to load your music library");
    } finally {
      setIsLoading(false);
    }
  };

  // Client-side filtering for special cases (like verified tracks)
  useEffect(() => {
    let filtered = [...tracks];

    // Only apply client-side filtering for "verified" status
    // Other filters are handled server-side
    if (selectedStatus === "verified") {
      filtered = filtered.filter(track => track.loop_verification?.is_seamless);
    }

    setFilteredTracks(filtered);
  }, [tracks, selectedStatus]);

  const handleTrackPlay = (track: UserTrack) => {
    console.log("Playing track:", track.title);

    // Stop current audio if playing
    if (audioElement) {
      audioElement.pause();
      audioElement.currentTime = 0;
    }

    // If clicking the same track that's playing, pause it
    if (currentPlayingTrack === track.uuid) {
      setCurrentPlayingTrack(null);
      setAudioElement(null);
      return;
    }

    // Create new audio element and play
    const audio = new Audio(track.file_url);
    audio.addEventListener('ended', () => {
      setCurrentPlayingTrack(null);
      setAudioElement(null);
    });

    audio.addEventListener('error', (e) => {
      console.error('Audio playback error:', e);
      toast.error('Failed to play audio');
      setCurrentPlayingTrack(null);
      setAudioElement(null);
    });

    audio.play().then(() => {
      setCurrentPlayingTrack(track.uuid);
      setAudioElement(audio);
    }).catch((error) => {
      console.error('Audio play failed:', error);
      toast.error('Failed to play audio');
    });
  };

  const handleTrackDownload = async (track: UserTrack) => {
    if (!track.file_url) {
      toast.error("Track file not available");
      return;
    }

    try {
      const link = document.createElement("a");
      link.href = track.file_url;
      link.download = `${track.title || "track"}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Download started");
    } catch (error) {
      toast.error("Download failed");
    }
  };

  const handleTrackDelete = async (track: UserTrack) => {
    if (!confirm("Are you sure you want to delete this track?")) {
      return;
    }

    try {
      // TODO: Implement delete API
      setTracks(prev => prev.filter(t => t.uuid !== track.uuid));
      toast.success("Track deleted successfully");
    } catch (error) {
      toast.error("Failed to delete track");
    }
  };

  const handleTrackShare = (track: UserTrack) => {
    const shareUrl = `${window.location.origin}/loops/${track.uuid}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success("Share link copied to clipboard");
  };





  const clearFilters = () => {
    setSearchQuery("");
    setSelectedStatus("all");
    setSelectedStyle("all");
    setSortBy("recent");
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing per page
  };

  // Search with debounce
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    // Clear existing timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }

    // Set new timer for debounced search
    const timer = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    setSearchDebounceTimer(timer);
  };

  if (status === "loading") {
    return (
      <div className="container py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your music library...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">My Music Library</h1>
          <p className="text-muted-foreground">
            Manage your AI-generated music loops and collections
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Link href="/generate">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Track
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Music className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Tracks</p>
                <p className="text-2xl font-bold">{tracks.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Duration</p>
                <p className="text-2xl font-bold">
                  {Math.round(tracks.reduce((sum, track) => sum + (track.duration || 0), 0) / 60)}m
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Music className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Public Tracks</p>
                <p className="text-2xl font-bold">
                  {tracks.filter(t => t.is_public).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">
                  {tracks.filter(t => {
                    const created = new Date(t.created_at || 0);
                    const now = new Date();
                    return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter & Search
              {/* Mobile filter toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
                className="md:hidden p-1 h-auto"
              >
                {isFilterPanelOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CardTitle>


          </div>
        </CardHeader>
        <CardContent>
          {/* Search bar - always visible */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your tracks..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Filter panel - collapsible on mobile */}
          <div className={`${isFilterPanelOpen ? 'block' : 'hidden'} md:block`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-4">

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
                <SelectItem value="verified">Loop Verified</SelectItem>
              </SelectContent>
            </Select>

            {/* Style Filter */}
            <Select value={selectedStyle} onValueChange={setSelectedStyle}>
              <SelectTrigger>
                <SelectValue placeholder="Style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Styles</SelectItem>
                <SelectItem value="electronic">Electronic</SelectItem>
                <SelectItem value="ambient">Ambient</SelectItem>
                <SelectItem value="corporate">Corporate</SelectItem>
                <SelectItem value="cinematic">Cinematic</SelectItem>
                <SelectItem value="acoustic">Acoustic</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="title">Title A-Z</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
              </SelectContent>
            </Select>

            {/* Per Page Selector */}
            <Select value={perPage.toString()} onValueChange={(value) => handlePerPageChange(parseInt(value))}>
              <SelectTrigger>
                <SelectValue placeholder="Per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="20">20 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
            </div>
          </div>



          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * perPage) + 1}-{Math.min(currentPage * perPage, totalTracks)} of {totalTracks} tracks
            </div>
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your tracks...</p>
        </div>
      ) : tracks.length === 0 ? (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks yet</h3>
          <p className="text-muted-foreground mb-4">
            Start creating your first AI music loop
          </p>
          <Link href="/generate">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Track
            </Button>
          </Link>
        </div>
      ) : filteredTracks.length === 0 ? (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or search terms
          </p>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTracks.map((track) => (
            <div key={track.uuid} className="relative">

              
              <div className="relative">
                <TrackCard
                  track={track}
                  showUser={false}
                  isPlaying={currentPlayingTrack === track.uuid}
                  onPlay={handleTrackPlay}
                  onPause={() => {
                    if (audioElement) {
                      audioElement.pause();
                      setCurrentPlayingTrack(null);
                      setAudioElement(null);
                    }
                  }}
                  onDownload={handleTrackDownload}
                  onShare={handleTrackShare}
                />

                {/* Custom actions overlay */}
                <div className="absolute top-2 right-2 flex items-center gap-1">
                  
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 space-y-4">
          <PaginationInfo
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalTracks}
            itemsPerPage={perPage}
          />
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="justify-center"
          />
        </div>
      )}
    </div>
  );
}
