"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useFavorite } from "@/hooks/use-favorite";
import "@/styles/audio-player.css";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Download,
  Share2,
  Heart,
  Clock,
  Music,
  Verified,
  FileText,
  Eye,
  EyeOff,
  Crown
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAudioPlayer } from "@/contexts/audio-player-context";
import { toast } from "sonner";
import { Track } from "@/types/music";
import { Play, Pause } from "lucide-react";


interface TrackDetailPageProps {
  track: Track;
  locale: string;
}

export default function TrackDetailPage({ track, locale }: TrackDetailPageProps) {
  const { data: session } = useSession();
  const { isLiked, isLoading: isFavoriteLoading, toggleFavorite } = useFavorite(track.uuid);
  const [downloadCount, setDownloadCount] = useState(track.download_count || 0);
  const [isPublic, setIsPublic] = useState(track.is_public);
  const [isUpdatingVisibility, setIsUpdatingVisibility] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // Use global audio player
  const {
    currentTrack,
    isPlaying: globalIsPlaying,
    play,
    pause,
    currentTime,
    duration: globalDuration,
    seek,
    volume,
    setVolume,
    toggleMute,
    isMuted,
    isLooping,
    toggleLoop
  } = useAudioPlayer();

  // Check if current user is the track owner
  const isOwner = session?.user && (
    track.user_uuid === (session.user as any).uuid ||
    track.user_uuid === session.user.id ||
    track.user_uuid === String(session.user.id)
  );

  // Check if user has premium access
  const isPremiumUser = !!session?.user;

  // Check if user can access this track
  const canAccessTrack = track.is_public || isOwner;

  // Check if this track is currently playing
  const isCurrentTrack = currentTrack?.uuid === track.uuid;
  const isTrackPlaying = isCurrentTrack && globalIsPlaying;
  const trackDuration = globalDuration || track.duration;

  // If track is private and user is not the owner, show access denied page
  if (!canAccessTrack) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="border-amber-200 bg-amber-50/50 dark:border-amber-800/30 dark:bg-amber-950/20">
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-amber-100 dark:bg-amber-900/50 rounded-full flex items-center justify-center">
                  <EyeOff className="h-8 w-8 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-amber-800 dark:text-amber-200 mb-2">
                    Private Music Track
                  </h2>
                  <p className="text-amber-700 dark:text-amber-300 mb-4">
                    This music track is set to private and can only be accessed by its creator.
                  </p>
                  <div className="bg-amber-100 dark:bg-amber-900/30 p-3 rounded-lg text-sm text-amber-800 dark:text-amber-200">
                    <p className="font-medium mb-1">🎵 Track exists but is not publicly available</p>
                    <p>If you believe you should have access to this track, please contact the creator.</p>
                  </div>
                </div>

                {!session ? (
                  <div className="pt-4 border-t border-amber-200 dark:border-amber-800/30">
                    <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
                      Are you the creator of this track?
                    </p>
                    <Button
                      onClick={() => window.location.href = '/auth/signin'}
                      className="bg-amber-600 hover:bg-amber-700 dark:bg-amber-700 dark:hover:bg-amber-600"
                    >
                      Sign In to Access
                    </Button>
                  </div>
                ) : (
                  <div className="pt-4 border-t border-amber-200 dark:border-amber-800/30">
                    <Button
                      variant="outline"
                      onClick={() => window.history.back()}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/30"
                    >
                      ← Go Back
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }



  const formatDate = (dateString?: string) => {
    if (!dateString) return "Unknown";
    return new Date(dateString).toLocaleDateString(locale === "en" ? "en-US" : locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const handleDownload = async () => {
    if (isDownloading) return;

    setIsDownloading(true);

    try {
      toast.info("Generating secure download link...");

      // Call the download API to get secure download URL
      const response = await fetch("/api/music/download", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          track_uuid: track.uuid,
          format: track.file_format || "wav",
          quality: "high",
          custom_filename: track.title ? `${track.title}.${track.file_format || "wav"}` : undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to generate download URL");
      }

      const response_data = await response.json();

      console.log("Download API response:", response_data);

      // 提取实际的数据部分
      const data = response_data.data || response_data;

      // Increment download count
      setDownloadCount(prev => prev + 1);

      // 验证下载URL
      if (!data.download_url) {
        console.error("No download_url in data:", data);
        throw new Error("No download URL received from API");
      }

      console.log("About to download from URL:", data.download_url);

      // 直接使用下载URL
      const downloadUrl = data.download_url;
      const filename = data.custom_filename || `${track.title || "loop"}.${data.format}`;

      console.log("Attempting download with URL:", downloadUrl);
      console.log("Filename:", filename);

      // 使用简单直接的方法下载
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      link.style.display = 'none';

      // 添加到DOM并触发点击
      document.body.appendChild(link);
      link.click();

      // 立即移除
      document.body.removeChild(link);

      console.log("Download initiated successfully");

      // Show success message with download method info
      const downloadMethod = data.download_method === 'cdn' ? 'CDN' : 'Direct';
      toast.success(`Download started via ${downloadMethod}!`);

      console.log("Download initiated:", {
        url: data.download_url,
        method: data.download_method,
        format: data.format,
        filename: data.custom_filename,
        expires_at: data.expires_at
      });

    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Download failed. Please try again.");
      console.error("Download error:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: track.title || "AI Music Loop",
          text: track.prompt || "Check out this AI-generated music loop!",
          url: window.location.href,
        });
        toast.success("Shared successfully!");
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      // Check if user cancelled the share
      if (error instanceof Error && error.name === 'AbortError') {
        // User cancelled sharing, don't show error
        return;
      }

      // For other errors, try clipboard fallback
      try {
        await navigator.clipboard.writeText(window.location.href);
        toast.success("Link copied to clipboard!");
      } catch (clipboardError) {
        toast.error("Sharing failed. Please try again.");
        console.error("Share error:", error);
      }
    }
  };

  const handleLike = () => {
    toggleFavorite();
  };

  const handlePlayPause = async () => {
    if (isTrackPlaying) {
      pause();
    } else {
      await play(track);
    }
  };

  const handleSeek = (value: number[]) => {
    const newTime = (value[0] / 100) * trackDuration;
    seek(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleDownloadCertificate = async () => {
    try {
      toast.info("Generating certificate...");

      console.log("Downloading certificate for track:", track.uuid);

      // Call certificate generation API
      const response = await fetch(`/api/music/certificate/${track.uuid}`, {
        method: "GET",
      });

      console.log("Certificate API response:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      if (response.ok) {
        const blob = await response.blob();
        console.log("Certificate blob size:", blob.size);

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        
        // Create safe filename for download
        const safeTitle = (track.title || "music").replace(/[<>:"/\\|?*]/g, '_');
        link.download = `${safeTitle}-certificate.pdf`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast.success("Certificate downloaded!");
      } else {
        // Try to get error message from response
        const errorText = await response.text();
        console.error("Certificate API error:", errorText);

        let errorMessage = "Failed to generate certificate";
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If not JSON, use the raw text
          if (errorText) {
            errorMessage = errorText;
          }
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Certificate download error:", error);
      toast.error("Failed to download certificate");
    }
  };

  const handleVisibilityChange = async (newVisibility: boolean) => {
    if (!isOwner || !isPremiumUser) {
      toast.error("Premium membership required");
      return;
    }

    setIsUpdatingVisibility(true);
    try {
      const response = await fetch(`/api/music/tracks/${track.uuid}/visibility`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ is_public: newVisibility }),
      });

      if (response.ok) {
        setIsPublic(newVisibility);
        toast.success(`Track is now ${newVisibility ? "public" : "private"}`);
      } else {
        toast.error("Failed to update visibility");
      }
    } catch (error) {
      console.error("Visibility update error:", error);
      toast.error("Failed to update visibility");
    } finally {
      setIsUpdatingVisibility(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">
                {track.title || "Untitled Loop"}
              </h1>
              <p className="text-lg text-muted-foreground">
                {track.prompt || "AI-generated seamless music loop"}
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLike}
                disabled={isFavoriteLoading}
                className={cn(isLiked && "text-red-500")}
              >
                <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
              </Button>

              {/* Download button - only for owners */}
              {isOwner && (
                <Button
                  onClick={handleDownload}
                  disabled={isDownloading}
                >
                  {isDownloading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-background border-t-transparent mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Track Metadata */}
          <div className="flex flex-wrap gap-2">
            {track.style && (
              <Badge variant="secondary">
                <Music className="h-3 w-3 mr-1" />
                {track.style}
              </Badge>
            )}
            {track.mood && (
              <Badge variant="outline">
                {track.mood}
              </Badge>
            )}
            {track.bpm && (
              <Badge variant="outline">
                {track.bpm} BPM
              </Badge>
            )}
            <Badge variant="outline">
              <Clock className="h-3 w-3 mr-1" />
              {track.duration}s
            </Badge>
            {track.loop_verification?.is_seamless && (
              <Badge variant="default" className="bg-green-500 dark:bg-green-600">
                <Verified className="h-3 w-3 mr-1" />
                Seamless Loop
              </Badge>
            )}
            <Badge variant={isPublic ? "default" : "secondary"}>
              {isPublic ? (
                <>
                  <Eye className="h-3 w-3 mr-1" />
                  Public
                </>
              ) : (
                <>
                  <EyeOff className="h-3 w-3 mr-1" />
                  Private
                </>
              )}
            </Badge>
          </div>
        </div>

        {/* Main Audio Player */}
        <Card>
          <CardContent className="p-6 space-y-4">
            {/* Waveform Visualization */}
            {track.waveform_data && (
              <div className="relative h-20 bg-muted rounded-md overflow-hidden">
                <div className="flex items-end h-full px-1">
                  {track.waveform_data.peaks.map((peak, index) => (
                    <div
                      key={index}
                      className="flex-1 bg-primary/60 mx-px rounded-sm cursor-pointer hover:bg-primary/80 transition-colors"
                      style={{
                        height: `${Math.max(2, peak * 100)}%`,
                        opacity: index / track.waveform_data!.peaks.length < (currentTime / trackDuration) ? 1 : 0.3,
                      }}
                      onClick={() => {
                        const clickTime = (index / track.waveform_data!.peaks.length) * trackDuration;
                        seek(clickTime);
                      }}
                    />
                  ))}
                </div>
                {/* Progress indicator */}
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-primary pointer-events-none"
                  style={{
                    left: `${(currentTime / trackDuration) * 100}%`,
                  }}
                />
              </div>
            )}

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="w-full">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={trackDuration > 0 ? (currentTime / trackDuration) * 100 : 0}
                  onChange={(e) => handleSeek([parseFloat(e.target.value)])}
                  className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(trackDuration)}</span>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleLoop}
                  className={cn(isLooping && "text-primary")}
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </Button>

                <Button
                  variant="default"
                  size="lg"
                  onClick={handlePlayPause}
                  className="w-12 h-12 rounded-full"
                >
                  {isTrackPlaying ? (
                    <Pause className="h-5 w-5" />
                  ) : (
                    <Play className="h-5 w-5 ml-0.5" />
                  )}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleMute}
                >
                  {isMuted || volume === 0 ? (
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                    </svg>
                  ) : (
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                    </svg>
                  )}
                </Button>
              </div>

              {/* Volume Control */}
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">Volume</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={isMuted ? 0 : volume * 100}
                  onChange={(e) => handleVolumeChange([parseFloat(e.target.value)])}
                  className="w-20 h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Creator Controls - Only visible to track owner with premium */}
        {isOwner && isPremiumUser && (
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-primary" />
                Creator Controls
                <Badge variant="secondary">
                  Premium
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Download Certificate */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Certificate</Label>
                  <Button
                    variant="outline"
                    onClick={handleDownloadCertificate}
                    className="w-full justify-start"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Download Certificate
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Official certificate for commercial use
                  </p>
                </div>

                {/* Visibility Control */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Visibility</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="visibility"
                      checked={isPublic}
                      onCheckedChange={handleVisibilityChange}
                      disabled={isUpdatingVisibility}
                    />
                    <Label htmlFor="visibility" className="text-sm">
                      {isPublic ? "Public" : "Private"}
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {isPublic
                      ? "Anyone can discover and play this track"
                      : "Only you can access this track"
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generation Prompt - Show if available */}
        {track.prompt && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Generation Prompt
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted/50 p-4 rounded-lg">
                <p className="text-sm leading-relaxed">
                  "{track.prompt}"
                </p>
              </div>
              {(track.style || track.mood || track.bpm || track.genre || track.instrument || track.theme) && (
                <div className="mt-3 pt-3 border-t">
                  <p className="text-xs text-muted-foreground mb-2">Generation parameters:</p>
                  <div className="space-y-3">
                    {/* Legacy single parameters */}
                    <div className="flex flex-wrap gap-2">
                      {track.style && (
                        <Badge variant="outline" className="text-xs">
                          Style: {track.style}
                        </Badge>
                      )}
                      {track.mood && (
                        <Badge variant="outline" className="text-xs">
                          Mood: {track.mood}
                        </Badge>
                      )}
                      {track.bpm && (
                        <Badge variant="outline" className="text-xs">
                          BPM: {track.bpm}
                        </Badge>
                      )}
                    </div>

                    {/* New array parameters */}
                    {track.genre && Array.isArray(track.genre) && track.genre.length > 0 && (
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Genres:</p>
                        <div className="flex flex-wrap gap-1">
                          {track.genre.map((genre, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {track.instrument && Array.isArray(track.instrument) && track.instrument.length > 0 && (
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Instruments:</p>
                        <div className="flex flex-wrap gap-1">
                          {track.instrument.map((instrument, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {instrument}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {track.theme && Array.isArray(track.theme) && track.theme.length > 0 && (
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Themes:</p>
                        <div className="flex flex-wrap gap-1">
                          {track.theme.map((theme, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {theme}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}



        {/* Track Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5" />
                Track Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Duration</span>
                <span className="text-sm">{track.duration}s</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">File Size</span>
                <span className="text-sm">{formatFileSize(track.file_size)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Format</span>
                <span className="text-sm uppercase">{track.file_format || "MP3"}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Created</span>
                <span className="text-sm">{formatDate(typeof track.created_at === 'string' ? track.created_at : track.created_at?.toISOString())}</span>
              </div>

              {isOwner && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Downloads</span>
                  <span className="text-sm">{downloadCount.toLocaleString()}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Loop Quality - Simplified */}
          {track.loop_verification ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Verified className="h-5 w-5" />
                  Loop Quality
                  <Badge variant={track.loop_verification.is_seamless ? "default" : "secondary"} className="ml-auto">
                    {Math.round(parseFloat(track.loop_verification.verification_score) * 100)}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {track.loop_verification.is_seamless
                    ? "✨ This track loops seamlessly with smooth transitions."
                    : "🎵 This track has good loop quality for most use cases."
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <Card className="border-dashed">
              <CardContent className="p-6">
                <div className="text-center space-y-2">
                  <Music className="h-8 w-8 mx-auto text-muted-foreground" />
                  <h4 className="font-medium text-muted-foreground">Optimized Loop</h4>
                  <p className="text-sm text-muted-foreground">
                    Generated with enhanced loop settings for seamless playback.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
